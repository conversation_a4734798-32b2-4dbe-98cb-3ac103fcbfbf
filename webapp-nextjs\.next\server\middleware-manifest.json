{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "2521603f2c4ad1b3caa52816a67337e2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3f0c56c2b2739279e6039699552d46022701625cbb491974aed2ce24faf6bea7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "89a747332f6634bd2247ac6a462c3f32db86730a12be0d3cb5f4c36ec0646edb"}}}, "sortedMiddleware": ["/"], "functions": {}}